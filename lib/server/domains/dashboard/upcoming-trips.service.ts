import { Squad } from "@/lib/domains/squad/squad.types"
import { Trip } from "@/lib/domains/trip/trip.types"
import { User } from "@/lib/domains/user/user.types"
import { UserServerService } from "../user/user.service"
import { SquadServerService } from "../squad/squad.service"
import { TripServerService } from "../trip/trip.service"

/**
 * Upcoming trips data structure for SSR
 */
export interface UpcomingTripsData {
  squads: Squad[]
  upcomingTrips: Trip[]
  tripsAttendeesDetails: Record<string, any[]>
}

/**
 * Server-side Upcoming Trips Service for optimized data fetching
 */
export class UpcomingTripsServerService {
  /**
   * Get upcoming trips data optimized for the upcoming trips page
   * @param userId User ID
   * @returns Upcoming trips data
   */
  static async getUpcomingTripsData(userId: string): Promise<UpcomingTripsData> {
    try {
      // Phase 1: Get user squads and upcoming trips in parallel
      const [squads, upcomingTrips] = await Promise.allSettled([
        SquadServerService.getUserSquads(userId),
        this.getUserUpcomingTrips(userId),
      ])

      // Handle potential failures
      const squadsData = squads.status === "fulfilled" ? squads.value : []
      const upcomingTripsData = upcomingTrips.status === "fulfilled" ? upcomingTrips.value : []

      // Phase 2: Get attendee details for upcoming trips
      const upcomingTripIds = upcomingTripsData.map((trip) => trip.id)
      const tripsAttendeesResult = await Promise.allSettled([
        this.getTripsAttendeesDetails(upcomingTripIds),
      ])

      const tripsAttendeesDetails =
        tripsAttendeesResult[0].status === "fulfilled" ? tripsAttendeesResult[0].value : {}

      return {
        squads: squadsData,
        upcomingTrips: upcomingTripsData,
        tripsAttendeesDetails,
      }
    } catch (error) {
      console.error("Error getting upcoming trips data (server):", error)
      throw error
    }
  }

  /**
   * Get upcoming trips for a user
   * @param userId User ID
   * @returns Array of upcoming trips
   */
  private static async getUserUpcomingTrips(userId: string): Promise<Trip[]> {
    try {
      // Get user squads first to find trips
      const squads = await SquadServerService.getUserSquads(userId)
      const squadIds = squads.map((squad) => squad.id)

      if (squadIds.length === 0) {
        return []
      }

      // Get all trips for user's squads and filter for upcoming
      const allTripsPromises = squadIds.map((squadId) => TripServerService.getSquadTrips(squadId))
      const allTripsResults = await Promise.allSettled(allTripsPromises)

      const allTrips: Trip[] = []
      allTripsResults.forEach((result) => {
        if (result.status === "fulfilled") {
          allTrips.push(...result.value)
        }
      })

      // Filter for upcoming trips (start date is in the future)
      const now = new Date()
      const upcomingTrips = allTrips.filter((trip) => {
        const startDate = new Date(trip.startDate)
        return startDate > now
      })

      // Sort by start date (earliest first)
      upcomingTrips.sort((a, b) => new Date(a.startDate).getTime() - new Date(b.startDate).getTime())

      return upcomingTrips
    } catch (error) {
      console.error("Error getting user upcoming trips (server):", error)
      return []
    }
  }

  /**
   * Get attendees details for multiple trips
   * @param tripIds Array of trip IDs
   * @returns Record of trip ID to attendees with user details
   */
  private static async getTripsAttendeesDetails(tripIds: string[]): Promise<Record<string, any[]>> {
    try {
      if (tripIds.length === 0) return {}

      // Get attendees for all trips in parallel
      const attendeesPromises = tripIds.map((tripId) =>
        TripServerService.getTripAttendeesWithDetails(tripId)
      )

      const attendeesResults = await Promise.allSettled(attendeesPromises)

      const tripsAttendeesDetails: Record<string, any[]> = {}

      attendeesResults.forEach((result, index) => {
        const tripId = tripIds[index]
        if (result.status === "fulfilled") {
          tripsAttendeesDetails[tripId] = result.value
        } else {
          tripsAttendeesDetails[tripId] = []
        }
      })

      return tripsAttendeesDetails
    } catch (error) {
      console.error("Error getting trips attendees details (server):", error)
      return {}
    }
  }

  /**
   * Get lightweight upcoming trips data for faster loading
   * @param userId User ID
   * @returns Essential upcoming trips data only
   */
  static async getLightweightUpcomingTripsData(userId: string): Promise<Partial<UpcomingTripsData>> {
    try {
      const [squads] = await Promise.allSettled([SquadServerService.getUserSquads(userId)])

      const squadsData = squads.status === "fulfilled" ? squads.value : []

      return {
        squads: squadsData,
        upcomingTrips: [],
        tripsAttendeesDetails: {},
      }
    } catch (error) {
      console.error("Error getting lightweight upcoming trips data (server):", error)
      throw error
    }
  }
}
