import { redirect } from "next/navigation"
import { AuthServerService } from "@/lib/server/domains/auth/auth.service"
import { MySquadsServerService } from "@/lib/server/domains/dashboard/my-squads.service"
import { MySquadsClient } from "./components/MySquadsClient"

/**
 * Server Component for My Squads Page
 * Fetches my squads data server-side for optimal performance
 */
export default async function MySquadsPage() {
  // Get authenticated user
  const user = await AuthServerService.getServerUser()

  if (!user) {
    redirect("/login")
  }

  // Check if user is new and should be redirected to welcome
  if (user.newUser === true) {
    redirect("/welcome")
  }

  try {
    // Fetch my squads data on the server
    const mySquadsData = await MySquadsServerService.getMySquadsData(user.uid)

    return <MySquadsClient initialData={mySquadsData} user={user} />
  } catch (error) {
    console.error("Error loading my squads data:", error)

    // Fallback to lightweight data if full data fetch fails
    try {
      const lightweightData = await MySquadsServerService.getLightweightMySquadsData(user.uid)

      return <MySquadsClient initialData={lightweightData} user={user} hasError={true} />
    } catch (fallbackError) {
      console.error("Error loading lightweight my squads data:", fallbackError)

      // Ultimate fallback - redirect to error page or show minimal UI
      redirect("/error?message=my-squads-load-failed")
    }
  }
}
