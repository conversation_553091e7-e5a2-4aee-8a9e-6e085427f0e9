import { redirect } from "next/navigation"
import { AuthServerService } from "@/lib/server/domains/auth/auth.service"
import { DashboardLayoutClient } from "./components/DashboardLayoutClient"

/**
 * Dashboard Layout - Shared layout for all dashboard routes
 * Handles authentication and provides navigation between dashboard sections
 */
export default async function DashboardLayout({ children }: { children: React.ReactNode }) {
  // Get authenticated user
  const user = await AuthServerService.getServerUser()

  if (!user) {
    redirect("/login")
  }

  // Check if user is new and should be redirected to welcome
  if (user.newUser === true) {
    redirect("/welcome")
  }

  return <DashboardLayoutClient user={user}>{children}</DashboardLayoutClient>
}
