import { redirect } from "next/navigation"
import { AuthServerService } from "@/lib/server/domains/auth/auth.service"
import { UpcomingTripsServerService } from "@/lib/server/domains/dashboard/upcoming-trips.service"
import { UpcomingTripsClient } from "./components/UpcomingTripsClient"

/**
 * Server Component for Upcoming Trips Page
 * Fetches upcoming trips data server-side for optimal performance
 */
export default async function UpcomingTripsPage() {
  // Get authenticated user
  const user = await AuthServerService.getServerUser()

  if (!user) {
    redirect("/login")
  }

  // Check if user is new and should be redirected to welcome
  if (user.newUser === true) {
    redirect("/welcome")
  }

  try {
    // Fetch upcoming trips data on the server
    const upcomingTripsData = await UpcomingTripsServerService.getUpcomingTripsData(user.uid)

    return <UpcomingTripsClient initialData={upcomingTripsData} user={user} />
  } catch (error) {
    console.error("Error loading upcoming trips data:", error)

    // Fallback to lightweight data if full data fetch fails
    try {
      const lightweightData = await UpcomingTripsServerService.getLightweightUpcomingTripsData(
        user.uid
      )

      return <UpcomingTripsClient initialData={lightweightData} user={user} hasError={true} />
    } catch (fallbackError) {
      console.error("Error loading lightweight upcoming trips data:", fallbackError)

      // Ultimate fallback - redirect to error page or show minimal UI
      redirect("/error?message=upcoming-trips-load-failed")
    }
  }
}
