"use client"

import { useEffect, useState, useCallback, useRef, useMemo } from "react"
import { useRealtimeUserSquads } from "@/lib/domains/squad/squad.realtime.hooks"
import { useRealtimeUserAllTrips } from "@/lib/domains/trip/trip.realtime.hooks"
import { useRealtimeTripsAttendeesDetails } from "@/lib/domains/user-trip/user-trip.realtime.hooks"
import { UpcomingTripsData } from "@/lib/server/domains/dashboard/upcoming-trips.service"

/**
 * Hook to sync server-side upcoming trips data with realtime updates
 * Provides seamless transition from SSR data to live data
 */
export function useRealtimeUpcomingTripsSync(
  setUpcomingTripsData: (
    updater: (prevData: Partial<UpcomingTripsData>) => Partial<UpcomingTripsData>
  ) => void,
  isHydrated: boolean
) {
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<Error | null>(null)
  const [hasInitialSync, setHasInitialSync] = useState(false)
  const syncTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  // Realtime hooks (only start after hydration)
  const { squads, loading: squadsLoading, error: squadsError } = useRealtimeUserSquads()
  const { upcomingTrips, loading: tripsLoading, error: tripsError } = useRealtimeUserAllTrips()

  // Get trip IDs for attendee details (memoized to prevent infinite re-renders)
  const upcomingTripIds = useMemo(() => upcomingTrips.map((trip) => trip.id), [upcomingTrips])
  const { tripsAttendeesDetails } = useRealtimeTripsAttendeesDetails(upcomingTripIds)

  // Debounced update to prevent excessive re-renders
  const updateUpcomingTripsData = useCallback(() => {
    if (!isHydrated) return

    // Clear any pending updates
    if (syncTimeoutRef.current) {
      clearTimeout(syncTimeoutRef.current)
    }

    // Debounce updates to prevent rapid re-renders
    syncTimeoutRef.current = setTimeout(() => {
      setUpcomingTripsData((prevData) => {
        // Only update if data has actually changed
        const newData = {
          ...prevData,
          squads,
          upcomingTrips,
          tripsAttendeesDetails,
        }

        // Simple comparison to avoid unnecessary updates
        const hasChanged =
          JSON.stringify(prevData.squads) !== JSON.stringify(squads) ||
          JSON.stringify(prevData.upcomingTrips) !== JSON.stringify(upcomingTrips) ||
          JSON.stringify(prevData.tripsAttendeesDetails) !== JSON.stringify(tripsAttendeesDetails)

        return hasChanged ? newData : prevData
      })

      setHasInitialSync(true)
    }, 100) // 100ms debounce
  }, [isHydrated, squads, upcomingTrips, tripsAttendeesDetails, setUpcomingTripsData])

  // Sync realtime data with upcoming trips state
  useEffect(() => {
    if (isHydrated && !squadsLoading && !tripsLoading) {
      updateUpcomingTripsData()
      setIsLoading(false)
    } else if (isHydrated && !hasInitialSync) {
      setIsLoading(true)
    }
  }, [isHydrated, squadsLoading, tripsLoading, updateUpcomingTripsData, hasInitialSync])

  // Handle errors from realtime subscriptions
  useEffect(() => {
    const errors = [squadsError, tripsError].filter(Boolean)
    if (errors.length > 0) {
      console.error("Realtime upcoming trips sync errors:", errors)
      setError(errors[0] as Error)
    } else {
      setError(null)
    }
  }, [squadsError, tripsError])

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (syncTimeoutRef.current) {
        clearTimeout(syncTimeoutRef.current)
      }
    }
  }, [])

  return {
    isLoading: isLoading || (isHydrated && !hasInitialSync && (squadsLoading || tripsLoading)),
    error,
    hasInitialSync,
  }
}
