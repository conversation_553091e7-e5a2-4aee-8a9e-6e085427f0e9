"use client"

import { useState, useEffect, useCallback, Suspense, lazy } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { DashboardSkeleton } from "./DashboardSkeleton"
import { DemoTourModal } from "@/components/demo-tour-modal"
import { User } from "@/lib/domains/user/user.types"
import { Trip } from "@/lib/domains/trip/trip.types"
import { Squad } from "@/lib/domains/squad/squad.types"
import { DashboardData } from "@/lib/server/domains/dashboard/dashboard.service"
import { useRealtimeSync } from "../hooks/useRealtimeSync"
import { useDemoTour } from "@/lib/domains/user/user.hooks"
import { DashboardImagePreloader } from "./DashboardImagePreloader"
import { DashboardPerformanceMonitor } from "./DashboardPerformanceMonitor"

const EMPTY_TRIPS: Trip[] = []
const EMPTY_SQUADS: Squad[] = []
// Lazy load tab components to reduce initial bundle size
const SquadsTab = lazy(() =>
  import("./SquadsTab").then((module) => ({ default: module.SquadsTab }))
)
const UpcomingTripsTab = lazy(() =>
  import("./UpcomingTripsTab").then((module) => ({ default: module.UpcomingTripsTab }))
)
const PastTripsTab = lazy(() =>
  import("./PastTripsTab").then((module) => ({ default: module.PastTripsTab }))
)

// Tab loading skeleton component
const TabSkeleton = () => (
  <div className="space-y-4">
    <div className="h-6 w-32 bg-muted animate-pulse rounded" />
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {[...Array(3)].map((_, i) => (
        <div key={i} className="h-64 bg-muted animate-pulse rounded-lg" />
      ))}
    </div>
  </div>
)

interface DashboardClientProps {
  initialData: Partial<DashboardData>
  user: User
  hasError?: boolean
}

export function DashboardClient({ initialData, user, hasError = false }: DashboardClientProps) {
  // State for dashboard data (initialized with server data)
  const [dashboardData, setDashboardData] = useState<Partial<DashboardData>>(initialData)
  const [isHydrated, setIsHydrated] = useState(false)

  // Demo tour state
  const { hasOptedOut, checking: demoTourChecking, refreshStatus } = useDemoTour()
  const [showDemoTour, setShowDemoTour] = useState(false)

  // Start realtime synchronization after hydration
  const {
    isLoading: realtimeLoading,
    error: realtimeError,
    hasInitialSync,
  } = useRealtimeSync(setDashboardData, isHydrated)

  // Mark as hydrated after initial render
  useEffect(() => {
    setIsHydrated(true)
  }, [])

  // Demo tour logic
  useEffect(() => {
    if (user && hasOptedOut === false && !demoTourChecking && !showDemoTour) {
      setShowDemoTour(true)
    } else if (hasOptedOut === true && showDemoTour) {
      setShowDemoTour(false)
    }
  }, [user, hasOptedOut, demoTourChecking, showDemoTour])

  // Handle demo tour close
  const handleDemoTourClose = useCallback(async () => {
    setShowDemoTour(false)
    await refreshStatus()
  }, [refreshStatus])

  // Determine loading state
  const isLoading =
    !isHydrated ||
    (realtimeLoading && !dashboardData.squads?.length && !dashboardData.upcomingTrips?.length)

  // Show skeleton only if we have no data and are loading
  if (
    isLoading &&
    !dashboardData.squads?.length &&
    !dashboardData.upcomingTrips?.length &&
    !dashboardData.pastTrips?.length
  ) {
    return <DashboardSkeleton />
  }

  return (
    <>
      {/* Performance monitoring */}
      <DashboardPerformanceMonitor
        hasInitialData={!!dashboardData.squads?.length || !!dashboardData.upcomingTrips?.length}
        hasRealtimeSync={hasInitialSync}
      />

      {/* Preload critical images for better LCP */}
      <DashboardImagePreloader
        upcomingTrips={dashboardData.upcomingTrips || EMPTY_TRIPS}
        pastTrips={dashboardData.pastTrips || EMPTY_TRIPS}
      />

      <div className="p-6">
        <div className="mb-6">
          <h1 className="text-3xl font-bold">Dashboard</h1>
          <p className="text-muted-foreground">Welcome back, {user?.displayName || "Friend"}!</p>
          {hasError && (
            <p className="text-sm text-yellow-600 mt-1">Some data may be outdated. Refreshing...</p>
          )}
        </div>

        <Tabs defaultValue="trips" className="space-y-4">
          <TabsList className="w-full">
            <TabsTrigger value="trips" className="flex-1 md:flex-initial">
              Upcoming Trips
            </TabsTrigger>
            <TabsTrigger value="squads" className="flex-1 md:flex-initial">
              My Squads
            </TabsTrigger>
            <TabsTrigger value="past" className="flex-1 md:flex-initial">
              Past Trips
            </TabsTrigger>
          </TabsList>

          <TabsContent value="squads">
            <Suspense fallback={<TabSkeleton />}>
              <SquadsTab
                squads={dashboardData.squads || EMPTY_SQUADS}
                upcomingTrips={dashboardData.upcomingTrips || EMPTY_TRIPS}
                loading={realtimeLoading}
                squadLeaders={dashboardData.squadLeaders}
              />
            </Suspense>
          </TabsContent>

          <TabsContent value="trips">
            <Suspense fallback={<TabSkeleton />}>
              <UpcomingTripsTab
                squads={dashboardData.squads || EMPTY_SQUADS}
                upcomingTrips={dashboardData.upcomingTrips || EMPTY_TRIPS}
                loading={realtimeLoading}
                tripsAttendeesDetails={dashboardData.tripsAttendeesDetails}
              />
            </Suspense>
          </TabsContent>

          <TabsContent value="past">
            <Suspense fallback={<TabSkeleton />}>
              <PastTripsTab
                squads={dashboardData.squads || EMPTY_SQUADS}
                pastTrips={dashboardData.pastTrips || EMPTY_TRIPS}
                loading={realtimeLoading}
              />
            </Suspense>
          </TabsContent>
        </Tabs>
      </div>

      {/* Demo Tour Modal */}
      <DemoTourModal isOpen={showDemoTour} onClose={handleDemoTourClose} />
    </>
  )
}
