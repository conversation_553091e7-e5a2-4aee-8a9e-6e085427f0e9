@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  font-family: Arial, Helvetica, sans-serif;
  /* Prevent layout shifts from scrollbar appearance/disappearance */
  overflow-y: scroll;
}

/* Logo color scheme support - uses CSS custom properties for dynamic theming */
.logo-fill {
  fill: hsl(var(--primary)); /* Uses primary color (Deep Teal in light mode, white in dark mode) */
  transition: fill 0.2s ease-in-out;
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  /* Hide scrollbar for Chrome, Safari and Opera */
  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }

  /* Hide scrollbar for IE, Edge and Firefox */
  .no-scrollbar {
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;  /* Firefox */
  }

  /* Thin scrollbar for better mobile UX */
  .scrollbar-thin::-webkit-scrollbar {
    height: 4px;
    width: 4px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    background: transparent;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    background: hsl(var(--muted-foreground));
    border-radius: 4px;
  }

  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: hsl(var(--muted-foreground)) transparent;
  }

  /* Floating AI Button - ensure proper viewport positioning */
  .floating-ai-button {
    position: fixed !important;
    bottom: 1.5rem !important;
    right: 1.5rem !important;
    z-index: 60 !important;
    pointer-events: none !important;
    width: 56px !important;
    height: 56px !important;
  }

  .floating-ai-button button {
    pointer-events: auto !important;
  }

  /* Prevent tooltip layout shifts */
  [data-radix-tooltip-content] {
    position: fixed !important;
    z-index: 9999 !important;
    pointer-events: none !important;
  }

  /* Ensure tooltip triggers don't cause layout shifts - only for buttons and interactive elements */
  button[data-radix-tooltip-trigger],
  [role="button"][data-radix-tooltip-trigger] {
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
  }

  /* Prevent dropdown menu layout shifts */
  [data-radix-dropdown-menu-content] {
    position: fixed !important;
    z-index: 50 !important;
    max-width: calc(100vw - 2rem) !important;
    /* Prevent content from causing horizontal scrollbars */
    box-sizing: border-box !important;
    /* Ensure dropdown doesn't affect document flow */
    transform: translateZ(0) !important;
  }

  /* Prevent all radix overlays from causing layout shifts */
  [data-radix-popper-content-wrapper] {
    position: fixed !important;
    z-index: 50 !important;
  }
}

@layer base {
  /* ===== CSS VARIABLES ===== */
  :root {
    /* Core theme colors */
    --background: 0 0% 96.08%;     /* Warm White #F5F5F5 */
    --foreground: 0 0% 13%;       /* Rich Black #212121 for headings */
    --card: 0 0% 100%;            /* Pure white for cards to contrast with warm white background */
    --card-foreground: 0 0% 25%;  /* Rich Black for card text */
    --popover: 0 0% 100%;         /* Pure white for popovers/toasts */
    --popover-foreground: 0 0% 25%; /* Rich Black for popover text */

    /* Interactive elements */
    --primary: 173.06 100% 23.73%;      /* Deep Teal #00796B for buttons */
    --primary-foreground: var(--background); /* Warm White text on teal buttons - same as background */
    --secondary: 0 0% 92%;        /* More contrast from background for visibility */
    --secondary-foreground: 0 0% 25%; /* Rich Black for secondary elements */
    --muted: 0 0% 92%;            /* More contrast from background for visibility */
    --muted-foreground: 0 0% 40%; /* Gray body text (neutral-600 equivalent) */
    --accent: var(--primary);       /* Deep Teal for accents/icons */
    --accent-foreground: var(--background); /* Warm White text on teal accents - same as background */

    /* Status colors */
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: var(--background);  /* Same as background #F5F5F5 */
    --warning: 38 92% 50%;          /* Orange for warnings */
    --warning-foreground: 0 0% 100%; /* White text on orange */
    --success: 142 76% 36%;         /* Green for success */
    --success-foreground: 0 0% 100%; /* White text on green */

    /* Form elements */
    --border: 0 0% 89.8%;
    --input: 0 0% 85%;            /* Light gray for unchecked switch background */
    --ring: var(--primary);   /* Deep Teal for focus rings */
    --ring-offset: #F5F5F5;       /* Match background for ring offset */

    /* Charts */
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;

    /* Sidebar components */
    --sidebar-background: var(--background);     /* Pure white for sidebar */
    --sidebar-foreground: 0 0% 13%;      /* Dark text on sidebar */
    --sidebar-primary: var(--primary); /* Deep Teal for active items */
    --sidebar-primary-foreground: 0 0% 100%; /* White text on teal */
    --sidebar-accent: 0 0% 96%;          /* Light gray for hover states */
    --sidebar-accent-foreground: 0 0% 13%; /* Dark text on hover */
    --sidebar-border: 0 0% 89%;          /* Light border */
    --sidebar-ring: 217.2 91.2% 59.8%;

    /* Component-specific variables */
    --toast-background: 0 0% 100%;  /* White background for toasts */
    --toast-foreground: 0 0% 13%;   /* Dark text on toasts */
    --toast-border: 0 0% 89%;       /* Light border for toasts */

    --tab-background: 0 0% 80%;     /* Light background for inactive tabs */
    --tab-foreground: 0 0% 45%;     /* Muted text for inactive tabs */
    --tab-active-background: 0 0% 100%; /* White background for active tabs */
    --tab-active-foreground: 0 0% 13%;  /* Dark text for active tabs */
    --tab-active-border: 0 0% 89%;  /* Light border for active tabs */

    --switch-background: 0 0% 89%;  /* Light gray for switch track */
    --switch-thumb: 0 0% 100%;      /* White for switch thumb */
    --switch-thumb-checked: 0 0% 100%; /* White for checked switch thumb */

    /* Custom teal colors for icons and backgrounds */
    --teal-600: 173.06 81% 28.73%;      /* Teal-600 equivalent for icons */
    --teal-100: 173.06 27% 93.73%;      /* Light teal background for icons */
  }

  .dark {
    /* Core theme colors */
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;

    /* Interactive elements */
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;

    /* Status colors */
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;

    /* Form elements */
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --ring-offset: #0a0a0a;         /* Dark background for ring offset */

    /* Charts */
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;

    /* Sidebar components */
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;

    /* Component-specific variables */
    --toast-background: 240 10% 3.9%; /* Dark background for toasts */
    --toast-foreground: 0 0% 98%;      /* Light text on toasts */
    --toast-border: 240 3.7% 15.9%;   /* Dark border for toasts */

    --tab-background: 240 3.7% 15.9%; /* Dark background for inactive tabs */
    --tab-foreground: 240 5% 64.9%;   /* Muted text for inactive tabs */
    --tab-active-background: 240 10% 3.9%; /* Darker background for active tabs */
    --tab-active-foreground: 0 0% 98%; /* Light text for active tabs */
    --tab-active-border: 240 3.7% 15.9%; /* Dark border for active tabs */

    --switch-background: 240 3.7% 15.9%; /* Dark gray for switch track */
    --switch-thumb: 240 5.9% 10%;        /* Dark for switch thumb */
    --switch-thumb-checked: 0 0% 98%;    /* Light for checked switch thumb */
  }

  /* ===== BASE STYLES ===== */
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    overflow-x: hidden;
  }

  html {
    overflow-x: hidden;
  }

  /* ===== ACCESSIBILITY FOCUS SYSTEM ===== */
  /* Ensure focus rings are visible and accessible in both themes */
  *:focus-visible {
    outline: 2px solid hsl(var(--ring));
    outline-offset: 2px;
  }

  /* Custom focus ring for components that need it */
  .focus-ring {
    @apply focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background;
  }

  /* Ensure interactive elements have proper focus indicators */
  button:focus-visible,
  [role="button"]:focus-visible,
  [role="tab"]:focus-visible,
  [role="switch"]:focus-visible,
  input:focus-visible,
  textarea:focus-visible,
  select:focus-visible {
    outline: 2px solid hsl(var(--ring));
    outline-offset: 2px;
  }
}

/* Theme transition styles */
:root {
  --theme-transition-duration: 300ms;
}

/* Apply transitions to the html and body elements */
html,
body {
  transition: background-color var(--theme-transition-duration) ease,
              color var(--theme-transition-duration) ease;
}

/* Prevent transitions during theme change to avoid flickering */
.theme-changing * {
  transition: none !important;
}

/* Apply transitions to common UI elements */
.theme-transition,
.theme-transition * {
  transition: background-color var(--theme-transition-duration) ease,
              border-color var(--theme-transition-duration) ease,
              color var(--theme-transition-duration) ease,
              fill var(--theme-transition-duration) ease,
              stroke var(--theme-transition-duration) ease,
              opacity var(--theme-transition-duration) ease;
}

/* Ensure content is visible after theme is applied */
.theme-aware-content {
  opacity: 1;
  transition: opacity var(--theme-transition-duration) ease;
}

/* Hide content during theme changes */
.theme-changing .theme-aware-content {
  opacity: 0.5;
}




